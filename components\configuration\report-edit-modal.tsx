import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  Input,
  Textarea,
  Switch,
  Spinner,
  Card,
  CardBody,
  CardHeader,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useReportDetail } from "@/hooks/reports/useReportDetail";
import { useReports } from "@/hooks/reports/useReports";
import { ApiReport, FieldTypeFilter } from "@/types/report";
import SelectableFieldsTable from "@/components/template/create/selectable-fields-table";
import ReportFiltersStep from "@/components/report/create/report-filters-step";

interface ReportEditModalProps {
  isOpen: boolean;
  report: ApiReport | null;
  onClose: () => void;
  onSave?: () => void;
}

export function ReportEditModal({
  isOpen,
  report,
  onClose,
  onSave,
}: ReportEditModalProps) {
  const { reportDetail, loading, error, fetchReportDetail, clearReportDetail } =
    useReportDetail();
  const { updateReport } = useReports();

  const [saving, setSaving] = useState(false);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [fieldFilters, setFieldFilters] = useState<
    Record<string, FieldTypeFilter>
  >({});
  const [reportData, setReportData] = useState({
    name: "",
    description: "",
    document_name: "",
    use_english_fields: false,
    include_observations: true,
  });

  useEffect(() => {
    if (isOpen && report) {
      fetchReportDetail(report.id.toString());
      setReportData({
        name: report.name,
        description: report.description,
        document_name: report.document_name,
        use_english_fields: report.use_english || false,
        include_observations: report.observations || false,
      });
    } else if (!isOpen) {
      clearReportDetail();
      setSelectedFields([]);
      setFieldFilters({});
    }
  }, [isOpen, report]);

  useEffect(() => {
    if (reportDetail) {
      // Set selected fields
      const fieldIds = reportDetail.fields?.map((f) => f.id.toString()) || [];
      setSelectedFields(fieldIds);

      // Set field filters from the new API format
      const filters: Record<string, FieldTypeFilter> = {};

      if (reportDetail.filters) {
        Object.entries(reportDetail.filters).forEach(([fieldType, filterConfig]) => {
          if (filterConfig.statuses && filterConfig.statuses.length > 0) {
            filters[fieldType] = {
              statuses: filterConfig.statuses,
              includeObservations: filterConfig.includeObservations || false,
            };
          }
        });
      }
      setFieldFilters(filters);
    }
  }, [reportDetail]);

  const handleSave = async () => {
    if (!report) return;

    try {
      setSaving(true);

      const result = await updateReport(report.id, {
        name: reportData.name,
        description: reportData.description,
        document_name: reportData.document_name,
        use_english: reportData.use_english_fields,
        observations: reportData.include_observations,
        filters: fieldFilters,
        active: report.active,
      });

      if (result.success) {
        if (onSave) {
          onSave();
        }
        onClose();
      } else {
        console.error("Failed to update report:", result.error);
      }
    } catch (error) {
      console.error("Error updating report:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    onClose();
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setReportData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (!report) return null;

  return (
    <Modal
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-xl font-medium">Editar Reporte</h3>
          <p className="text-small text-default-500">
            Modifica la configuración del reporte
          </p>
        </ModalHeader>
        <ModalBody className="py-4">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-danger">{error}</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <Icon icon="heroicons:document-text" width={20} />
                    <h4 className="text-lg font-semibold">
                      Información básica
                    </h4>
                  </div>
                </CardHeader>
                <CardBody className="space-y-4">
                  <Input
                    label="Nombre del reporte"
                    value={reportData.name}
                    variant="bordered"
                    onValueChange={(value) => handleInputChange("name", value)}
                  />
                  <Textarea
                    label="Descripción"
                    value={reportData.description}
                    variant="bordered"
                    onValueChange={(value) =>
                      handleInputChange("description", value)
                    }
                  />
                  <Input
                    label="Nombre del documento"
                    value={reportData.document_name}
                    variant="bordered"
                    onValueChange={(value) =>
                      handleInputChange("document_name", value)
                    }
                  />
                  <div className="flex items-center justify-between p-3 bg-default-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Icon
                        className="text-default-500"
                        icon="heroicons:language"
                        width={20}
                      />
                      <div>
                        <p className="font-medium">Idioma de los campos</p>
                        <p className="text-sm text-default-500">
                          Usar nombres en inglés para los campos
                        </p>
                      </div>
                    </div>
                    <Switch
                      isSelected={reportData.use_english_fields}
                      onValueChange={(value) =>
                        handleInputChange("use_english_fields", value)
                      }
                    />
                  </div>
                </CardBody>
              </Card>

              {/* Tabs for Fields and Filters */}
              <Tabs
                aria-label="Opciones de edición del reporte"
                className="w-full"
              >
                <Tab
                  key="fields"
                  title={
                    <div className="flex items-center space-x-2">
                      <Icon icon="heroicons:squares-2x2" />
                      <span>Campos ({selectedFields.length})</span>
                    </div>
                  }
                >
                  <div className="">
                    <SelectableFieldsTable
                      selectedFields={selectedFields}
                      onSelectionChange={setSelectedFields}
                    />
                  </div>
                </Tab>
                <Tab
                  key="filters"
                  title={
                    <div className="flex items-center space-x-2">
                      <Icon icon="heroicons:funnel" />
                      <span>Filtros ({Object.keys(fieldFilters).length})</span>
                    </div>
                  }
                >
                  <div className="">
                    <ReportFiltersStep
                      fieldFilters={fieldFilters}
                      selectedFields={selectedFields}
                      onFiltersChange={setFieldFilters}
                    />
                  </div>
                </Tab>
              </Tabs>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={handleClose}>
            Cancelar
          </Button>
          <Button
            color="primary"
            isDisabled={loading || saving}
            isLoading={saving}
            onPress={handleSave}
          >
            {saving ? "Guardando..." : "Guardar Cambios"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
